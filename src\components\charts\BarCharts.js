import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
} from "recharts";

const BarChartComponent = ({ chartData, isChart, dimension, isWidth }) => {
  const [barColors, setBarColors] = useState(() => {
    const initialColors = ["#EDDF82", "#82C3ED", "#82EDAD", "#ED8282"];
    return initialColors.concat(
      Array.from({ length: chartData.y_axis.length - 4 }, () =>
        generateRandomColor()
      )
    );
  });

  const transformedData =
    chartData.x_axis && chartData.x_axis.length > 0
      ? chartData.x_axis
          .map((item, index) => {
            if (typeof item === "object" && item !== null) {
              const xAxisKey = Object.keys(item)[0];
              return {
                name: item[xAxisKey],
                ...chartData.y_axis[index],
              };
            }
            return null;
          })
          .filter(<PERSON><PERSON><PERSON>)
      : [];

  const formatYAxis = (tick) => {
    if (tick >= 100000) {
      return `${tick / 100000}L`;
    } else if (tick >= 1000) {
      return `${tick / 1000}k`;
    }
    return tick;
  };
  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      return (
        <div
          className="custom-tooltip"
          style={{
            backgroundColor: "white",
            padding: "10px",
            border: "1px solid #ccc",
            fontSize: "14px",
          }}
        >
          <div>{payload[0]?.payload?.name}</div>
          {payload.map((entry, index) => (
            <div key={index} style={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </div>
          ))}
        </div>
      );
    }
    return null;
  };

  const truncateXAxisName = (name) => {
    return name.length > 10 ? `${name.substring(0, 10)}...` : name;
  };

  function generateRandomColor() {
    const r = Math.floor(Math.random() * 168) + 100;
    const g = Math.floor(Math.random() * 168) + 100;
    const b = Math.floor(Math.random() * 168) + 100;
    return `rgb(${r}, ${g}, ${b})`;
  }

  return (
    <ResponsiveContainer
      width={dimension ? dimension.w * 120 : "100%"}
      height={dimension ? dimension.h * 70 : 400}
    >
      <BarChart
        data={transformedData}
        margin={{
          top: dimension ? 20 : 35,
          right: dimension ? 10 : 30,
          left: isWidth ? 0 : dimension ? 15 : 5,
          bottom: isWidth ? 70 : dimension ? 40 : 60,
        }}
        barGap={0}
        barCategoryGap={0}
      >
        <XAxis
          dataKey="name"
          tick={{ fontSize: dimension ? 10 : 12, fontWeight: 600 }}
          angle={dimension ? -35 : -45}
          textAnchor="end"
          tickFormatter={truncateXAxisName}
        />
        <YAxis
          tick={{ fontSize: dimension ? 10 : 12, fontWeight: 600 }}
          tickCount={10}
          tickFormatter={formatYAxis}
        />
        <Tooltip content={<CustomTooltip />} />
        {!isChart || isWidth ? (
          <Legend
            verticalAlign="top"
            layout="horizontal"
            wrapperStyle={{
              position: "absolute",
              top: "10%",
              right: isWidth ? "" : "5%",
              left: isWidth ? "" : "5%",
              transform: "translateY(-80%)",
              fontWeight: 600,
            }}
          />
        ) : null}
        {chartData.y_axis && chartData.y_axis.length > 0 ? (
          Object.keys(chartData.y_axis[0]).map((key, index) => (
            <Bar key={key} dataKey={key} fill={barColors[index]} barSize={15} />
          ))
        ) : (
          <text
            x="50%"
            y="50%"
            textAnchor="middle"
            dominantBaseline="middle"
            fontSize="16px"
          >
            No records to display
          </text>
        )}
      </BarChart>
    </ResponsiveContainer>
  );
};

export default BarChartComponent;
