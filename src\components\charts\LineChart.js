import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  ResponsiveContainer,
} from "recharts";
import dayjs from "dayjs";

const LineChartComponent = ({ data, colors, dimension, isChart, isWidth }) => {
  const dateFormatter = (tick) => {
    return dayjs(tick).format("DD MMM HH:mm");
  };
  const formatYAxis = (tick) => {
    if (tick >= 100000) {
      return `${tick / 100000}L`;
    } else if (tick >= 1000) {
      return `${tick / 1000}k`;
    }
    return tick;
  };

  const legendWrapperStyle = {
    position: "absolute",
    top: "5%",
    left: isWidth ? "20%" : "50%",
    transform: "translateY(-80%)",
    fontWeight: 400,
    fontSize: "12px",
  };

  const transposedData = data?.x_axis
    ? data.x_axis.map((xItem, index) => ({
        Datetime: xItem.Datetime,
        ...Object.keys(data?.y_axis[index]).reduce((acc, key) => {
          acc[key] = data?.y_axis[index][key];
          acc["Datetime"] = xItem.Datetime;
          return acc;
        }, {}),
      }))
    : [];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div
          className="custom-tooltip"
          style={{
            backgroundColor: "white",
            padding: "10px",
            border: "1px solid #ccc",
            fontSize: "14px",
          }}
        >
          <div className="p-1">
            <span>{`Datetime: ${dateFormatter(label)}`}</span>
            <br />
            {payload.map((item, index) => (
              <div key={index} style={{ color: item.color }}>
                <span>{`${item.name}: ${item.value}`}</span>
                <br />
              </div>
            ))}
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer
      width={dimension ? dimension.w * 120 : "100%"}
      height={dimension ? dimension.h * 70 : 400}
    >
      <LineChart
        data={transposedData}
        margin={{
          top: dimension ? 20 : 35,
          right: dimension ? 10 : 30,
          left: isWidth ? 0 : dimension ? 15 : 5,
          bottom: isWidth ? 70 : dimension ? 40 : 60,
        }}
        barGap={0}
        barCategoryGap={0}
      >
        <Tooltip content={<CustomTooltip />} />
        <XAxis
          dataKey="Datetime"
          type="category"
          tick={{ fontSize: dimension ? 10 : 12, fontWeight: 600 }}
          tickFormatter={dateFormatter}
          angle={-85}
          textAnchor="end"
          interval={0}
        />
        <YAxis
          tick={{ fontSize: dimension ? 10 : 12, fontWeight: 600 }}
          tickCount={dimension ? 7 : 10}
          tickFormatter={formatYAxis}
          interval={dimension ? 0 : 2}
        />
        {!isChart || isWidth ? (
          <Legend
            verticalAlign="top"
            layout="horizontal"
            wrapperStyle={{
              position: "absolute",
              top: "5%",
              right: isWidth ? "" : "5%",
              left: isWidth ? "" : "5%",
              transform: "translateY(-80%)",
              fontWeight: 600,
            }}
          />
        ) : null}

        {data?.y_axis &&
        data?.y_axis?.length > 0 &&
        Object.keys(data?.y_axis[0])?.length > 0 ? (
          Object.keys(data?.y_axis[0]).map((key, index) => (
            <Line
              key={key}
              type="natural"
              dataKey={key}
              stroke={colors[index % colors.length]}
              dot={{ strokeWidth: 1.5, r: 3 }}
              strokeWidth={3}
            />
          ))
        ) : (
          <text
            x="50%"
            y="50%"
            textAnchor="middle"
            dominantBaseline="middle"
            fontSize="16px"
          >
            No records to display
          </text>
        )}
      </LineChart>
    </ResponsiveContainer>
  );
};

export default LineChartComponent;
