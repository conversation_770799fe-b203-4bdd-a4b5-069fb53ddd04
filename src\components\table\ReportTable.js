import React from "react";
import {
  MaterialReactTable,
  useMaterialReactTable,
  MRT_ShowHideColumnsButton,
} from "material-react-table";
import { SearchIcon } from "../../icons";
import UnfoldMoreIcon from "@mui/icons-material/UnfoldMore";
import InputAdornment from "@mui/material/InputAdornment";
import styled from "styled-components";
import theme from "../../tailwind-theme";
import { Box } from "@mui/material";

const TableWrapper = styled.div`
  border: 1px solid #d1d5db; /* gray-300 */
  border-radius: 0.3rem; /* rounded-md in Tailwind */

  .MuiTableCell-head {
    background-color: ${theme.backgroundColor.bgTable} !important;
    color: #000000 !important;
    position: sticky;
    top: 0;
    z-index: 1;
    font-weight: 500px !important;
  }

  .MuiTableCell-root.MuiTableCell-head[data-pinned="true"]:before {
    background-color: ${theme.backgroundColor.bgTable} !important;
  }

  .MuiTableHead-root {
    position: sticky;
    top: 0;
    z-index: 2;
  }
`;

const CustomTable = ({
  data,
  columns,
  isLoading,
  onRowClick,
  maxWidthh,
  columnHide,
}) => {
  const enhancedColumns = columns.map((column) => ({
    ...column,
    filterFn: (row, id, filterValue) => {
      const trimmedFilterValue = filterValue.trimStart();
      const cellValue = row.getValue(id);
      if (cellValue == null) return false;
      const cellValueString = String(cellValue).toLowerCase();
      return cellValueString.includes(trimmedFilterValue.toLowerCase());
    },
  }));

  const table = useMaterialReactTable({
    columns: enhancedColumns,
    data,
    enableColumnActions: columnHide ? true : false,
    enableTopToolbar: columnHide ? true : false,
    enableBottomToolbar: false,
    enableCellActions: true,
    enableColumnPinning: true,
    enableRowSelection: false,
    enablePagination: false,
    enableSorting: true,
    enableStickyHeader: true,
    enableHiding: columnHide ? true : false,
    muiTableContainerProps: {
      sx: { maxHeight: maxWidthh ? maxWidthh : "60vh" },
      lg: { maxHeight: "72vh" },
    },
    icons: {
      SortIcon: (props) => <UnfoldMoreIcon {...props} />,
    },
    initialState: {
      showColumnFilters: true,
      enableToolbar: false,
      enableColumnFilters: true,
      enableSorting: true,
      columnPinning: {
        left: ["mrt-row-select"],
        right: ["actions"],
      },
    },
    state: {
      isLoading: isLoading,
    },
    enableRowVirtualization: true,
    getRowId: (originalRow, index) => originalRow?.id ?? `row-${index}`,
    muiTableBodyRowProps: ({ row }) => ({
      onClick: () => {
        if (onRowClick) {
          onRowClick(row);
        }
      },
    }),

    muiTablePaperProps: {
      sx: { boxShadow: "0", borderRadius: "3px" },
    },
    muiTableHeadCellProps: {
      sx: {
        backgroundColor: "#374150",
        fontSize: "12px",
        color: "#ffffff",
        fontFamily: "OpenSanHebrew",
        "& .Mui-TableHeadCell-Content-Wrapper": {
          whiteSpace: "nowrap",
        },
      },
    },
    muiSelectCheckboxProps: {},
    muiFilterTextFieldProps: {
      placeholder: "Search",
      InputProps: {
        startAdornment: (
          <InputAdornment position="start">
            <SearchIcon />
          </InputAdornment>
        ),
        style: {
          backgroundColor: "#ffffff",
          height: "30px",
          fontSize: 12,
          boxShadow: "0px 4px 4px 0px #0000001A",
        },
      },
      variant: "outlined",
    },
    renderEmptyRowsFallback: () => (
      <div
        style={{
          padding: "25px",
          fontSize: "16px",
          color: "#808080",
          fontStyle: "italic",
          marginLeft: "400px",
        }}
      >
        No records to display
      </div>
    ),
    // Custom toolbar to show only column visibility button when columnHide is enabled
    renderToolbarInternalActions: columnHide
      ? ({ table }) => (
          <Box>
            <MRT_ShowHideColumnsButton table={table} />
          </Box>
        )
      : undefined,
  });

  return (
    <TableWrapper>
      <MaterialReactTable table={table} />
    </TableWrapper>
  );
};

export default CustomTable;
