import { Formik, Form, ErrorMessage, Field } from "formik";
import Title from "../Title";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";
import TextField from "../components/FormsUI/TextField";
import Button from "../components/Button/Button";
import OutlinedButton from "../components/Button/OutlinedButton";
import bgImage from "../assets/img/Records.png";
import {
  protocolTypeOptions,
  interfaceTypeOptions,
  transactionType,
  CDRSearch_conditional_filter,
} from "../common/constants";
import { useMemo, useState, useRef, useContext } from "react";
import { useQuery, useMutation } from "react-query";
import { CalendarIcon, NoFilterIcon, CloseIcon, Alert } from "../icons";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "../components/DatePicker/styles.css";
import {
  customerList,
  destinationCountry,
  destinationName,
  supplierList,
} from "../services/dropdown-api";
import { downloadCDR, searchCDR } from "../services/cdrsearch-api";
import Pagination from "../components/Pagination/Pagination";
import theme from "../tailwind-theme";
import ResultPerPageComponent from "../components/Pagination/ResultsPerPage";
import { DataContext } from "../context/DataContext";
import ExportPopup from "../popups/exportpopup";
import { AuthContext } from "../context/AuthContext";
import { DownloadContext } from "../context/DownloadContext";
import ErrorDialog from "../popups/ErrorDialog";
import CustomDropDown from "../components/Dropdown/ReportsDropdownList";
import InfoModal from "../components/modals/InfoModal";
import SuccessDialog from "../popups/SuccessDialog";
import ExpandableTable from "../components/ExpandableTable";
import ReportTable from "../components/table/ReportTable";
import {
  buildFilterData,
  CDRvalidation,
  generateFilename,
  getFormattedDateTimeRange,
  TimeComponent,
} from "../components/Timer";

const CDRSearch = () => {
  const [data, setData] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [clearData, setClearData] = useState(false);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [showExportConfirmation, setShowExportConfirmation] = useState(false);
  const { configApiData, roles } = useContext(AuthContext);
  const [customerOptions, setCustomerOptions] = useState([]);
  const [supplierOptions, setSupplierOptions] = useState([]);
  const formRef = useRef(null);
  const [columnsData, setColumnsData] = useState([]);
  const [filterData, setFilterData] = useState({});
  const {
    resultPerPage,
    setSupplierBindList,
    setCustomerBindList,
    customerBindList,
    supplierBindList,
    destnationNameList,
    setDestinationNameList,
    destinationCountryList,
    setDestinationCountryList,
  } = useContext(DataContext);
  const [limitPerPage, setLimitPerPage] = useState(100);
  const [currentPage, setCurrentPage] = useState(1);
  const [download, setDownload] = useState(false);
  const [applyClicked, setApplyClicked] = useState(false);
  const [downloadType, setDownloadType] = useState("PDF");
  const [isApplyClicked, setIsApplyClicked] = useState(false);
  const { setIsDownloading } = useContext(DownloadContext);
  const [successDialog, setSuccessDialog] = useState(false);
  const [showAlertConfirmation, setShowAlertConfirmation] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [message, setMessage] = useState("");
  const { user } = useContext(AuthContext);
  const {
    filterDataDetail,
    setFilterDataDetail,
    viewDataFilter,
    setViewDataFilter,
    calendarDate,
    setCalendarDate,
    timeDate,
    setTimeDate,
  } = useContext(DataContext);

  const permissions = useMemo(() => {
    return roles?.resources?.filter((res) => res.name === "CDR Search")[0]
      .permissions;
  }, [roles]);
  const isAdmin = user.isSuperAdmin;

  const handleDownload = () => {
    if (permissions?.download === 0) {
      setMessage("Download permission not allowed");
      setShowAlertConfirmation(true);
    } else {
      setShowExportConfirmation(true);
      setShowAlertConfirmation(false);
    }
  };

  /////////////////////////////////////////////////API CALLS////////////////////////////////////////////
  useQuery(["getCustomer"], customerList, {
    onSuccess: (resp) => {
      let ndata = resp?.data
        ?.map((x) => ({ label: x.name, value: x.name }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );

      ndata = [{ label: "Select All", value: "Select All" }, ...ndata];
      setCustomerOptions(ndata);

      const customerBindList = [
        { label: "Select All", value: "Select All" },
        ...(resp?.data?.map((type) => ({
          value: type.customer_bind,
          label: type.customer_bind,
        })) || []),
      ];

      setCustomerBindList(customerBindList);
    },
  });

  useQuery(["getSupplier"], supplierList, {
    onSuccess: (resp) => {
      let ndata = resp?.data
        ?.map((x) => ({ label: x.name, value: x.name }))
        .filter(
          (item, index, self) =>
            index === self.findIndex((t) => t.label === item.label)
        );

      ndata = [{ label: "Select All", value: "Select All" }, ...ndata];
      setSupplierOptions(ndata);

      const supplierBindList = [
        { label: "Select All", value: "Select All" },
        ...(resp?.data?.map((type) => ({
          value: type.supplier_bind,
          label: type.supplier_bind,
        })) || []),
      ];

      setSupplierBindList(supplierBindList);
    },
  });

  useQuery(["/destinationCountryData"], destinationCountry, {
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      const list = [
        { label: "Select All", value: "Select All" },
        ...(res?.data?.map((type) => ({ value: type, label: type })) || []),
      ];
      setDestinationCountryList(list);
    },
  });

  useQuery(["/destinationNameData"], destinationName, {
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      const list = [
        { label: "Select All", value: "Select All" },
        ...(res?.data?.map((type) => ({ value: type, label: type })) || []),
      ];
      setDestinationNameList(list);
    },
  });

  const {
    mutate: searchCDRAPI,
    isLoading: isSearchLoading,
    data: searchData,
  } = useMutation(searchCDR);

  const { mutate: downloadCDRAPI, isLoading: isDownloadLoading } =
    useMutation(downloadCDR);

  /////////////////////////////////////////////////API CALLS END////////////////////////////////////////////
  const initialValues = useMemo(
    () => ({
      customerName: filterDataDetail?.customerName || [],
      supplierName: filterDataDetail?.supplierName || [],
      customerBind: filterDataDetail?.customerBind || [],
      supplierBind: filterDataDetail?.supplierBind || [],
      protocolType: filterDataDetail?.traffic_type || [],
      interfaceType: filterDataDetail?.interfaceType || [],
      messageId: filterDataDetail?.messageId || "",
      aNumber: filterDataDetail?.aNumber || "",
      bNumber: filterDataDetail?.bNumber || "",
      destMcc: filterDataDetail?.destMcc || "",
      destMnc: filterDataDetail?.destMnc || "",
      destinationName: filterDataDetail?.destinationName || [],
      destinationCountry: filterDataDetail?.destinationCountry || [],
      transactionType: filterDataDetail?.transactionType || [],
      selectedStartDate: null,
      selectedEndDate: null,
      startTime: {
        hours: timeDate?.startTime?.hours || "00",
        minutes: timeDate?.startTime?.minutes || "00",
        seconds: timeDate?.startTime?.seconds || "00",
      },
      selectedDate: calendarDate || null,
      endTime: {
        hours: timeDate?.endTime?.hours || "00",
        minutes: timeDate?.endTime?.minutes || "00",
        seconds: timeDate?.endTime?.seconds || "00",
      },
      viewCDR: viewDataFilter || 1,
    }),
    []
  );

  const handleLimitChange = (e, values) => {
    setLimitPerPage(e?.target?.value);
    setCurrentPage(1);
    searchCDRAPICall(values, parseInt(e?.target?.value), 1);
  };

  const searchCDRAPICall = (values, limitValue, pageNumber) => {
    setFilterDataDetail(values);
    const filters = buildFilterData(values);
    const { startFormatted, endFormatted, timeDifferenceInHours } =
      getFormattedDateTimeRange(values);

    const reqObj = {
      filters,
      download,
      type: download ? downloadType : "",
      limit: limitValue ?? limitPerPage,
      page: pageNumber ?? currentPage,
      cdrType: values.viewCDR === 1 ? "single" : "multiple",
      startDate: isApplyClicked ? startFormatted : "",
      endDate: isApplyClicked ? endFormatted : "",
    };

    if (
      timeDifferenceInHours > CDRSearch_conditional_filter &&
      !values.customerBind?.length &&
      !values.supplierBind?.length &&
      !values.destinationName?.length
    ) {
      setMessage(
        `Please select at least Customer Bind/Supplier Bind/Destination Name when time period is more than ${CDRSearch_conditional_filter} hours.`
      );
      setShowAlertConfirmation(true);
      return;
    }

    if (
      timeDifferenceInHours < CDRSearch_conditional_filter &&
      filters.length === 0
    ) {
      setMessage("Please select at least one filter.");
      setShowAlertConfirmation(true);
      return;
    }

    setFilterData(reqObj);
    searchCDRAPI(
      { reqObj },
      {
        onSuccess: (resp) => {
          const records = resp?.data?.data || [];
          setColumnsData(
            records.length
              ? Object.keys(records[0]).map((x) => ({
                  header: x,
                  accessorKey: x,
                }))
              : []
          );
          setData(records);
        },
        onError: (error) => {
          const errorMessage =
            error.code === "ERR_NETWORK"
              ? "Something went wrong! Please check the connectivity!"
              : error?.response?.data?.message || "Something went wrong!";
          setErrorMessage(errorMessage);
          setShowErrorDialog(true);
        },
      }
    );
  };
  const downloadCDRAPICall = (values, type) => {
    setFilterDataDetail(values);
    const filters = buildFilterData(values);
    const { startFormatted, endFormatted } = getFormattedDateTimeRange(values);

    if (
      searchData?.data?.totalCount < configApiData.INITIATE_OFFLINE_DOWNLOAD
    ) {
      setIsDownloading(true);
    }

    let reqObj = {
      filters,
      download: 1,
      type,
      limit: "",
      page: currentPage,
      startDate: isApplyClicked ? startFormatted : "",
      endDate: isApplyClicked ? endFormatted : "",
      totalCount: searchData?.data?.totalCount,
      cdrType: values.viewCDR === 1 ? "single" : "multiple",
    };
    const finalFilename = generateFilename(reqObj);
    reqObj.fileName = finalFilename;

    downloadCDRAPI(
      {
        reqObj,
        totalCount: searchData?.data?.totalCount,
        maxCount: configApiData.INITIATE_OFFLINE_DOWNLOAD,
      },
      {
        onSuccess: (resp) => {
          const total = searchData?.data?.totalCount;
          if (total < configApiData.INITIATE_OFFLINE_DOWNLOAD) {
            setIsDownloading(false);
            const url = URL.createObjectURL(resp.data);
            const link = document.createElement("a");
            link.href = url;
            link.download = finalFilename;
            link.click();
          } else {
            setSuccessDialog(true);
            setMessage(resp.data.message);
          }
        },
        onError: (error) => {
          setIsDownloading(false);
          const errorMessage =
            error.code === "ERR_NETWORK"
              ? "Something went wrong! Please check the connectivity!"
              : "Something went wrong!";
          setErrorMessage(errorMessage);
          setShowErrorDialog(true);
        },
      }
    );
  };

  const toggleTable = () => setIsExpanded((prev) => !prev);
  const ChevronIcon = ({ direction = "down" }) => {
    return direction === "down" ? (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fillRule="evenodd"
          d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
          clipRule="evenodd"
        />
      </svg>
    ) : (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fillRule="evenodd"
          d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
          clipRule="evenodd"
        />
      </svg>
    );
  };

  return (
    <div>
      <div className="mt-10">
        <Title title={"CDR Search"} />
      </div>
      <div className="bg-white py-5 px-6 mt-10">
        <div
          onClick={toggleTable}
          className="cursor-pointer px-1 py-1 font-semibold text-sm flex items-center gap-1 underline"
        >
          <span>Select filters</span>
          <ChevronIcon direction={isExpanded ? "up" : "down"} />
        </div>

        <div>
          <Formik
            initialValues={initialValues}
            enableReinitialize={true}
            innerRef={formRef}
            validationSchema={CDRvalidation(configApiData, isAdmin)}
            onSubmit={(values) => {
              setViewDataFilter(values.viewCDR);
              setApplyClicked(true);
              setDownload(false);
              setCurrentPage(1);
              setDownloadType("");
              setClearData(false);
              searchCDRAPICall(values, null, 1);
            }}
          >
            {({
              errors,
              resetForm,
              dirty,
              values,
              setFieldValue,
              handleBlur,
              touched,
            }) => (
              <Form>
                {isExpanded && (
                  <div className="border border-panelBorder px-5 pt-5">
                    <div className=" flex justify-end w-full  ">
                      <div className="relative">
                        <div className="flex gap-2 items-center mb-2">
                          <InputLabel
                            label={"Please select date range"}
                            isMandatory={true}
                          />
                          <CalendarIcon
                            className="cursor-pointer"
                            onClick={() => {
                              setOpenDialog(!openDialog);
                            }}
                          ></CalendarIcon>
                        </div>

                        <ErrorMessage
                          name="selectedDate"
                          component={"div"}
                          className="text-xs text-errorColor"
                        ></ErrorMessage>

                        {openDialog && (
                          <>
                            <div className="absolute top-6 right-0 px-1 py-1 border border-outerBorder rounded-sm bg-white min-w-[200px] min-h-[218px]  z-10">
                              <div className="w-full flex justify-end">
                                <CloseIcon
                                  className="w-2.5 h-2.5 cursor-pointer"
                                  onClick={() => setOpenDialog(false)}
                                />
                              </div>
                              <div className="flex flex-row">
                                <DatePicker
                                  name="selectedDate"
                                  startDate={calendarDate?.[0]}
                                  endDate={calendarDate?.[1]}
                                  selectsRange
                                  inline
                                  onChange={(dates) => {
                                    setFieldValue("selectedDate", dates);
                                    setCalendarDate(dates);
                                  }}
                                  onBlur={handleBlur}
                                  maxDate={new Date()}
                                />

                                <div className="border-l border-inputTextBoxBorder">
                                  <div className="border-b border-inputTextBoxBorder py-5 text-center font-bold text-sm">
                                    {" "}
                                    Time Range
                                  </div>
                                  <TimeComponent
                                    key="start"
                                    label="Start Time"
                                    name="startTime"
                                    values={values}
                                    setFieldValue={setFieldValue}
                                    setTimeDate={setTimeDate}
                                  ></TimeComponent>
                                  <TimeComponent
                                    key="end"
                                    label="End Time"
                                    name="endTime"
                                    values={values}
                                    setFieldValue={setFieldValue}
                                    setTimeDate={setTimeDate}
                                  ></TimeComponent>
                                </div>
                              </div>

                              <div>
                                <p
                                  name="selectedDate"
                                  component={"div"}
                                  className="text-xs text-errorColor"
                                >
                                  {errors?.selectedDate}
                                </p>
                                <button
                                  className={`mt-2 w-full h-9 bg-bgSecondary text-sm text-white rounded-[3px] mb-2 cursor-pointer disabled:bg-opacity-40`}
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setIsApplyClicked(true);
                                    setOpenDialog(false);
                                  }}
                                  disabled={
                                    !values.selectedDate || errors?.selectedDate
                                  }
                                >
                                  Apply
                                </button>
                              </div>
                            </div>
                          </>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-x-0 md:gap-x-5 gap-y-2 md:gap-y-5 ">
                      <div>
                        <InputLabel
                          label={"Customer Name"}
                          isMandatory={isAdmin ? false : true}
                        />
                        <CustomDropDown
                          btnWidth="min-w-[100%]"
                          data={customerOptions}
                          btnName={"Select Customer Name"}
                          onSelectionChange={(selectedDetail) => {
                            setFieldValue("customerName", selectedDetail);
                          }}
                          value={values.customerName}
                          defaultSelectedData={
                            filterDataDetail.customerName
                              ? filterDataDetail.customerName
                              : []
                          }
                        />
                        {errors.customerName && touched.customerName && (
                          <div className="text-red-500 text-xs ml-2">
                            {errors.customerName}
                          </div>
                        )}
                      </div>
                      <div>
                        <InputLabel label={"Customer Bind"} />
                        <CustomDropDown
                          btnWidth="min-w-[100%]"
                          data={customerBindList}
                          btnName={"Select Customer Bind"}
                          onSelectionChange={(selectedDetail) => {
                            setFieldValue("customerBind", selectedDetail);
                          }}
                          value={values.customerBind}
                          defaultSelectedData={
                            filterDataDetail.customerBind
                              ? filterDataDetail.customerBind
                              : []
                          }
                        />
                        {errors.customerBind && touched.customerBind && (
                          <div className="text-red-500 text-xs ml-2">
                            {errors.customerBind}
                          </div>
                        )}
                      </div>
                      <div>
                        <InputLabel
                          label={"Supplier Name"}
                          isMandatory={isAdmin ? false : true}
                        />
                        <CustomDropDown
                          btnWidth="min-w-[100%]"
                          data={supplierOptions}
                          btnName={"Select Supplier Name"}
                          onSelectionChange={(selectedDetail) => {
                            setFieldValue("supplierName", selectedDetail);
                          }}
                          value={values.supplierName}
                          defaultSelectedData={
                            filterDataDetail.supplierName
                              ? filterDataDetail.supplierName
                              : []
                          }
                        />
                        {errors.supplierName && touched.supplierName && (
                          <div className="text-red-500 text-xs ml-2">
                            {errors.supplierName}
                          </div>
                        )}
                      </div>
                      <div>
                        <InputLabel label={"Supplier Bind"} />
                        <CustomDropDown
                          btnWidth="min-w-[100%]"
                          data={supplierBindList}
                          btnName={"Select Bind Name"}
                          onSelectionChange={(selectedDetail) => {
                            setFieldValue("supplierBind", selectedDetail);
                          }}
                          value={values.supplierBind}
                          defaultSelectedData={
                            filterDataDetail.supplierBind
                              ? filterDataDetail.supplierBind
                              : []
                          }
                        />
                        {errors.supplierBind && touched.supplierBind && (
                          <div className="text-red-500 text-xs ml-2">
                            {errors.supplierBind}
                          </div>
                        )}
                      </div>
                      <div>
                        <InputLabel label={"Traffic Type"} />
                        <CustomDropDown
                          btnWidth="min-w-[100%]"
                          data={protocolTypeOptions}
                          btnName={"Select Traffic Type"}
                          onSelectionChange={(selectedDetail) => {
                            setFieldValue("protocolType", selectedDetail);
                          }}
                          value={values.protocolType}
                          defaultSelectedData={
                            filterDataDetail.protocolType
                              ? filterDataDetail.protocolType
                              : []
                          }
                        />
                        {errors.protocolType && touched.protocolType && (
                          <div className="text-red-500 text-xs ml-2">
                            {errors.protocolType}
                          </div>
                        )}
                      </div>
                      <div>
                        <InputLabel label={"Supplier Interface Type"} />
                        <CustomDropDown
                          btnWidth="min-w-[100%]"
                          data={interfaceTypeOptions}
                          btnName={"Select Supplier Interface Type"}
                          onSelectionChange={(selectedDetail) => {
                            setFieldValue("interfaceType", selectedDetail);
                          }}
                          value={values.interfaceType}
                          defaultSelectedData={
                            filterDataDetail.interfaceType
                              ? filterDataDetail.interfaceType
                              : []
                          }
                        />
                        {errors.interfaceType && touched.interfaceType && (
                          <div className="text-red-500 text-xs ml-2">
                            {errors.interfaceType}
                          </div>
                        )}
                      </div>
                      <div>
                        <InputLabel label={"Message ID"} />
                        <TextField
                          name="messageId"
                          placeholder={"Enter Message ID"}
                        />
                      </div>
                      <div>
                        <InputLabel label={"A Number"} />
                        <TextField
                          name="aNumber"
                          placeholder={"Enter A Number"}
                        />
                      </div>
                      <div>
                        <InputLabel label={"B Number"} />
                        <TextField
                          name="bNumber"
                          placeholder={"Enter B Number"}
                        />
                      </div>
                      <div>
                        <InputLabel label={"Destination MCC"} />
                        <TextField
                          name="destMcc"
                          placeholder={"Destination MCC"}
                        />
                      </div>
                      <div>
                        <InputLabel label={"Destination MNC"} />
                        <TextField
                          name="destMnc"
                          placeholder={"Destination MNC"}
                        />
                      </div>
                      <div>
                        <InputLabel label={"Destination Name"} />
                        <CustomDropDown
                          btnWidth="min-w-[100%]"
                          data={destnationNameList}
                          btnName={"Select Destination Name"}
                          onSelectionChange={(selectedDetail) => {
                            setFieldValue("destinationName", selectedDetail);
                          }}
                          value={values.destinationName}
                          defaultSelectedData={
                            filterDataDetail.destinationName
                              ? filterDataDetail.destinationName
                              : []
                          }
                        />
                        {errors.destinationName && touched.destinationName && (
                          <div className="text-red-500 text-xs ml-2">
                            {errors.destinationName}
                          </div>
                        )}
                      </div>
                      <div>
                        <InputLabel label={"Destination Country Name"} />
                        <CustomDropDown
                          btnWidth="min-w-[100%]"
                          data={destinationCountryList}
                          btnName={"Select Destination Country Name"}
                          onSelectionChange={(selectedDetail) => {
                            setFieldValue("destinationCountry", selectedDetail);
                          }}
                          value={values.destinationCountry}
                          defaultSelectedData={
                            filterDataDetail.destinationCountry
                              ? filterDataDetail.destinationCountry
                              : []
                          }
                        />
                        {errors.destinationCountry &&
                          touched.destinationCountry && (
                            <div className="text-red-500 text-xs ml-2">
                              {errors.destinationCountry}
                            </div>
                          )}
                      </div>
                      <div>
                        <InputLabel
                          label={"Transaction Flow Type"}
                          isMandatory={true}
                        />
                        <CustomDropDown
                          btnWidth="min-w-[100%]"
                          data={transactionType}
                          btnName={"Select Transaction Flow Type"}
                          onSelectionChange={(selectedDetail) => {
                            setFieldValue("transactionType", selectedDetail);
                          }}
                          value={values.transactionType}
                          defaultSelectedData={
                            filterDataDetail.transactionType
                              ? filterDataDetail.transactionType
                              : []
                          }
                        />
                        {errors.transactionType && touched.transactionType && (
                          <div className="text-red-500 text-xs ml-2">
                            {errors.transactionType}
                          </div>
                        )}
                      </div>

                      <div>
                        <InputLabel label={"CDR View Type"} />
                        <div className="flex gap-5 mt-3">
                          <label className="flex items-center gap-2">
                            <Field
                              type="radio"
                              name="viewCDR"
                              value={1}
                              as="input"
                              checked={values.viewCDR === 1}
                              onChange={() => setFieldValue("viewCDR", 1)}
                              className="accent-black"
                            />
                            <p className="text-sm">Single Liner</p>
                          </label>
                          <label className="flex items-center gap-2">
                            <Field
                              type="radio"
                              name="viewCDR"
                              value={2}
                              as="input"
                              checked={values.viewCDR === 2}
                              onChange={() => setFieldValue("viewCDR", 2)}
                              className="accent-black"
                            />
                            <p className="text-sm">Multiple Liner</p>
                          </label>
                        </div>
                      </div>
                    </div>
                    <div className="mt-10 mb-5 flex justify-end gap-x-5">
                      <Button
                        label="Clear"
                        type="reset"
                        buttonClassName={"w-40"}
                        onClick={() => {
                          setFilterDataDetail({});
                          setApplyClicked(false);
                          setIsApplyClicked(false);
                          setCalendarDate(null);
                          setClearData(true);
                          setViewDataFilter(1);
                          setTimeDate({
                            startTime: {
                              hours: "00",
                              minutes: "00",
                            },
                            endTime: {
                              hours: "00",
                              minutes: "00",
                            },
                          });
                          resetForm({ values: initialValues });
                          setFieldValue("transactionType", []);
                          setOpenDialog(false);
                        }}
                      ></Button>
                      <OutlinedButton
                        label="Apply"
                        type="submit"
                        buttonClassName={"w-40"}
                        disabled={isSearchLoading}
                      ></OutlinedButton>
                    </div>
                  </div>
                )}

                {!applyClicked && !(searchData?.data?.totalCount > 0) ? (
                  <div className="border border-panelBorder mt-7 py-8">
                    <div className="text-center font-bold text-xl">
                      Apply filters to generate the data
                      <NoFilterIcon
                        className="mx-auto"
                        height="317px"
                        width="320px"
                      />
                    </div>
                  </div>
                ) : (
                  <>
                    {searchData?.data?.totalCount > 0 ? (
                      <div className="flex justify-end my-5">
                        <OutlinedButton
                          label="Download"
                          disabled={isDownloadLoading || clearData}
                          buttonClassName="text-xs w-36 text-white h-10 rounded-md"
                          onClick={() => {
                            if (searchData?.data?.totalCount === 0) {
                              setShowErrorDialog(true);
                              setErrorMessage("No data to download");
                            } else {
                              handleDownload();
                            }
                          }}
                        />
                      </div>
                    ) : (
                      <div className="mb-5" />
                    )}

                    {searchData?.data?.totalCount > 0 && !clearData ? (
                      viewDataFilter === 1 ? (
                        <div className="mt-7">
                          <ExpandableTable
                            columns={columnsData}
                            data={data || []}
                            isLoading={isSearchLoading}
                            filterData={filterData}
                          />
                        </div>
                      ) : (
                        <div className="mt-7">
                          <ReportTable
                            columns={columnsData}
                            data={data || []}
                            isLoading={isSearchLoading}
                          />
                        </div>
                      )
                    ) : (
                      <div className="border border-outerBorder p-10">
                        <div className="flex text-headingColor text-xl justify-center">
                          {isSearchLoading
                            ? "Loading..."
                            : "Oops! No records to display."}
                        </div>
                        <div className="flex justify-center my-12">
                          <img
                            alt="ima"
                            src={bgImage}
                            style={{
                              height: "10%",
                              width: "10%",
                              objectFit: "cover",
                            }}
                          />
                        </div>
                      </div>
                    )}

                    {searchData?.data?.totalCount > 0 && isApplyClicked && (
                      <div className="flex justify-between items-center mt-5">
                        <div className="flex items-center">
                          <ResultPerPageComponent
                            countPerPage={resultPerPage}
                            limit={limitPerPage}
                            handleLimitChange={(e) =>
                              handleLimitChange(e, values)
                            }
                            pageName="CDRSearch"
                          />
                          <div
                            style={{
                              fontSize: "14px",
                              paddingLeft: "10px",
                              color: theme.textColor.titleColor,
                            }}
                          >
                            {(currentPage - 1) * limitPerPage + 1} -{" "}
                            {Math.min(
                              limitPerPage * currentPage,
                              searchData?.data?.totalCount
                            )}{" "}
                            of {searchData?.data?.totalCount} rows
                          </div>
                        </div>
                        <Pagination
                          className="pagination-bar"
                          currentPage={currentPage}
                          totalCount={searchData?.data?.totalCount}
                          pageSize={limitPerPage}
                          onPageChange={(page) => {
                            setCurrentPage(() => page);
                            searchCDRAPICall(values, null, page);
                          }}
                        />
                      </div>
                    )}
                  </>
                )}

                <ExportPopup
                  show={showExportConfirmation}
                  onHide={() => setShowExportConfirmation(false)}
                  onConfirm={(type) => {
                    setShowExportConfirmation(false);
                    downloadCDRAPICall(values, type);
                  }}
                  title={"Export"}
                  identity={"CDR export"}
                />
              </Form>
            )}
          </Formik>
        </div>

        <InfoModal
          icon={<Alert className="w-10 h-10 " />}
          show={showAlertConfirmation}
          onHide={() => setShowAlertConfirmation(false)}
          message={message}
        />
        <ErrorDialog
          show={showErrorDialog}
          onHide={() => setShowErrorDialog(false)}
          message={errorMessage}
        />
        <SuccessDialog
          show={successDialog}
          onHide={() => setSuccessDialog(false)}
          message={message}
        />
      </div>
    </div>
  );
};
export default CDRSearch;
